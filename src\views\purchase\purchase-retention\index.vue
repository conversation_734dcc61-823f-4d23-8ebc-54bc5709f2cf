<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
					<el-form-item label="食材名称" prop="foodName">
						<el-input placeholder="请输入食材名称" v-model="state.queryForm.foodName" />
					</el-form-item>
					<el-form-item label="供应商" prop="supplierName">
						<el-input placeholder="请输入供应商名称" v-model="state.queryForm.supplierName" />
					</el-form-item>
					<el-form-item label="留样日期" prop="sampleDate">
						<el-date-picker
							type="date"
							placeholder="请选择留样日期"
							v-model="state.queryForm.sampleDate"
							value-format="YYYY-MM-DD"
						></el-date-picker>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList"> 查询</el-button>
						<el-button icon="Refresh" @click="resetQuery">重置</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()">
						新增留样
					</el-button>
					<el-button plain :disabled="multiple" icon="Delete" type="primary" @click="handleDelete(selectObjs)">
						删除
					</el-button>
					<right-toolbar
						v-model:showSearch="showSearch"
						:export="'retention_sample_export'"
						@exportExcel="exportExcel"
						class="ml10 mr20"
						style="float: right"
						@queryTable="getDataList"
					></right-toolbar>
				</div>
			</el-row>
			<el-table
				:data="state.dataList"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				@selection-change="selectionChangHandle"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column prop="foodName" label="食材名称" show-overflow-tooltip />
				<el-table-column prop="supplierName" label="供应商" show-overflow-tooltip />
				<el-table-column prop="category" label="食材分类" show-overflow-tooltip />
				<el-table-column prop="sampleImages" label="留样图片" width="120">
					<template #default="scope">
						<div class="img-box" v-if="scope.row.sampleImages">
							<el-image
								v-for="m in scope.row.sampleImages.split(',')"
								:key="m"
								:src="m.includes('http') ? m : baseURL + m"
								:preview-src-list="[m.includes('http') ? m : baseURL + m]"
								fit="cover"
								:preview-teleported="true"
								:hide-on-click-modal="true"
							></el-image>
						</div>
						<div class="noPic" v-else>-</div>
					</template>
				</el-table-column>
				<el-table-column prop="sampleDate" label="留样日期" show-overflow-tooltip />
				<el-table-column prop="sampleTime" label="留样时间" show-overflow-tooltip />
				<el-table-column prop="sampleWeight" label="留样重量(g)" show-overflow-tooltip />
				<el-table-column prop="storageLocation" label="存储位置" show-overflow-tooltip />
				<el-table-column prop="temperature" label="存储温度(℃)" show-overflow-tooltip />
				<el-table-column prop="expiryDate" label="保质期至" show-overflow-tooltip />
				<el-table-column prop="samplePerson" label="留样人员" show-overflow-tooltip />
				<el-table-column prop="status" label="状态" width="80">
					<template #default="scope">
						<el-tag :type="scope.row.status === '正常' ? 'success' : scope.row.status === '已过期' ? 'danger' : 'warning'">
							{{ scope.row.status }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="150">
					<template #default="scope">
						<el-button icon="edit-pen" text type="primary" @click="formDialogRef.openDialog(scope.row.id)">
							编辑
						</el-button>
						<el-button icon="delete" text type="primary" @click="handleDelete([scope.row.id])">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="systemOrganization">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchList, delObjs } from '/@/api/admin/organization';
import { useMessage, useMessageBox } from '/@/hooks/message';

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
// 定义查询字典

// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields();
	// 清空多选
	selectObjs.value = [];
	getDataList();
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/admin/organization/export', Object.assign(state.queryForm, { ids: selectObjs }), 'organization.xlsx');
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm('此操作将永久删除');
	} catch {
		return;
	}

	try {
		await delObjs(ids);
		getDataList();
		useMessage().success('删除成功');
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
</script>
<style lang="scss" scoped>
.img-box {
	display: flex;
	flex-direction: row;

	.el-image {
		width: 40px;
		height: 40px;
		margin-right: 10px;
	}
}
</style>
